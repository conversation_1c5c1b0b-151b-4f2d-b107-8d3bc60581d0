'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileUploader } from '@/components/file-uploader';
import { useS3ImageUpload } from '@/hooks/useS3Upload';
// Alternative: import { useDirectS3Upload } from '@/hooks/useS3UploadAlternative';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Link, X } from 'lucide-react';

interface ImageUploadFieldProps {
  label: string;
  value: string;
  onChange: (url: string) => void;
  placeholder?: string;
  uploadPath?: string;
  disabled?: boolean;
}

export function ImageUploadField({
  label,
  value,
  onChange,
  placeholder = 'Enter image URL or upload an image',
  uploadPath = '/uploads/cms',
  disabled = false
}: ImageUploadFieldProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [activeTab, setActiveTab] = useState<'url' | 'upload'>('url');

  const { upload, isUploading } = useS3ImageUpload({
    onSuccess: (data) => {
      onChange(data.url);
      setFiles([]);
      setActiveTab('url');
    }
  });

  const handleFileUpload = async (uploadFiles: File[]) => {
    if (uploadFiles.length === 0) return;

    const file = uploadFiles[0];
    const fileName = `${Date.now()}-${file.name}`;

    try {
      await upload({
        file,
        fileName,
        path: uploadPath
      });
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleRemoveImage = () => {
    onChange('');
    setFiles([]);
  };

  return (
    <div className='space-y-3'>
      <Label htmlFor={`${label.toLowerCase().replace(/\s+/g, '-')}`}>
        {label}
      </Label>

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'url' | 'upload')}
      >
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='url' className='flex items-center gap-2'>
            <Link className='h-4 w-4' />
            URL
          </TabsTrigger>
          <TabsTrigger value='upload' className='flex items-center gap-2'>
            <Upload className='h-4 w-4' />
            Upload
          </TabsTrigger>
        </TabsList>

        <TabsContent value='url' className='space-y-3'>
          <div className='flex gap-2'>
            <Input
              id={`${label.toLowerCase().replace(/\s+/g, '-')}`}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              type='url'
              disabled={disabled}
              className='flex-1'
            />
            {value && (
              <Button
                type='button'
                variant='outline'
                size='icon'
                onClick={handleRemoveImage}
                disabled={disabled}
              >
                <X className='h-4 w-4' />
              </Button>
            )}
          </div>
        </TabsContent>

        <TabsContent value='upload' className='space-y-3'>
          <FileUploader
            value={files}
            onValueChange={setFiles}
            onUpload={handleFileUpload}
            maxFiles={1}
            maxSize={5 * 1024 * 1024} // 5MB
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }}
            disabled={disabled || isUploading}
          />
        </TabsContent>
      </Tabs>

      {/* Image Preview */}
      {value && (
        <div className='relative w-full max-w-sm'>
          <div className='relative aspect-video w-full overflow-hidden rounded-lg border'>
            <Image
              src={value}
              alt={`${label} preview`}
              fill
              className='object-cover'
              onError={() => {
                // Handle broken image - could show placeholder or error state
              }}
            />
          </div>
          <Button
            type='button'
            variant='destructive'
            size='sm'
            className='absolute -top-2 -right-2'
            onClick={handleRemoveImage}
            disabled={disabled}
          >
            <X className='h-3 w-3' />
          </Button>
        </div>
      )}
    </div>
  );
}
